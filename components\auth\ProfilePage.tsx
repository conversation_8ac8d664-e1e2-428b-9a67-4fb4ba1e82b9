import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useAddress } from '../../contexts/AddressContext';
import { UserIcon, MailIcon, PhoneIcon, GlobeAltIcon, LogoutIcon, PencilIcon, MapPinIcon } from '../common/Icon';

const ProfilePage: React.FC = () => {
  const { user, profile, signOut, updateProfile, loading } = useAuth();
  const { addresses } = useAddress();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    countryCode: '',
    country: '',
  });

  useEffect(() => {
    setIsVisible(true);
    
    // Redirect if not logged in
    if (!loading && !user) {
      navigate('/login');
      return;
    }

    // Populate form data when profile is loaded
    if (profile) {
      setFormData({
        firstName: profile.first_name || '',
        lastName: profile.last_name || '',
        phoneNumber: profile.phone_number || '',
        countryCode: profile.country_code || '',
        country: profile.country || '',
      });
    }
  }, [user, profile, loading, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setUpdating(true);
    setError('');
    setMessage('');

    const updates = {
      first_name: formData.firstName.trim(),
      last_name: formData.lastName.trim(),
      phone_number: formData.phoneNumber.trim() || undefined,
      country_code: formData.countryCode || undefined,
      country: formData.country || undefined,
    };

    const { error } = await updateProfile(updates);

    if (error) {
      setError(error.message);
    } else {
      setMessage('Profile updated successfully!');
      setIsEditing(false);
    }

    setUpdating(false);
  };

  const handleSignOut = async () => {
    const { error } = await signOut();
    if (error) {
      setError(error.message);
    } else {
      navigate('/');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 py-6 px-3 sm:py-12 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background Elements - Optimized for mobile */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-5 w-32 h-32 sm:top-20 sm:left-20 sm:w-64 sm:h-64 bg-brand-accent-teal/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-10 right-5 w-40 h-40 sm:bottom-20 sm:right-20 sm:w-80 sm:h-80 bg-brand-main-red/10 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/4 w-20 h-20 sm:left-1/3 sm:w-32 sm:h-32 bg-white/20 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-2xl mx-auto">
        {/* Header - Mobile Optimized */}
        <div className={`text-center mb-6 sm:mb-8 transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <img
            src="/assets/logo.png"
            alt="Le Prestine Logo"
            className="h-12 sm:h-16 w-auto mx-auto mb-4 sm:mb-6"
          />
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 font-serif">
            My Profile
          </h2>
          <p className="mt-1 sm:mt-2 text-sm sm:text-base text-gray-600">
            Manage your account information
          </p>
        </div>

        {/* Profile Card - Mobile Optimized */}
        <div className={`
          bg-white/80 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 p-4 sm:p-8
          transition-all duration-1200 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}
        `} style={{ transitionDelay: '200ms' }}>
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-2xl sm:rounded-3xl pointer-events-none"></div>

          <div className="relative">
            {/* Messages - Mobile Optimized */}
            {message && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-3 py-2 sm:px-4 sm:py-3 rounded-xl sm:rounded-2xl text-sm mb-4 sm:mb-6">
                {message}
              </div>
            )}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 sm:px-4 sm:py-3 rounded-xl sm:rounded-2xl text-sm mb-4 sm:mb-6">
                {error}
              </div>
            )}

            {/* Profile Header - Mobile Responsive */}
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 mb-6 sm:mb-8">
              <div className="flex items-center space-x-3 sm:space-x-4">
                <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-brand-accent-teal to-brand-accent-teal-darker rounded-full flex items-center justify-center shadow-lg">
                  <UserIcon className="text-white" size={20} />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg sm:text-xl font-bold text-gray-900 truncate">
                    {profile?.first_name} {profile?.last_name}
                  </h3>
                  <p className="text-sm sm:text-base text-gray-600 truncate">{user.email}</p>
                </div>
              </div>

              {/* Mobile-First Action Buttons */}
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="flex items-center justify-center space-x-2 px-4 py-2.5 sm:py-2 bg-brand-accent-teal text-white rounded-xl hover:bg-brand-accent-teal-darker transition-colors touch-manipulation min-h-[44px] sm:min-h-0"
                >
                  <PencilIcon size={16} />
                  <span className="font-medium">{isEditing ? 'Cancel' : 'Edit'}</span>
                </button>

                <button
                  onClick={handleSignOut}
                  className="flex items-center justify-center space-x-2 px-4 py-2.5 sm:py-2 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors touch-manipulation min-h-[44px] sm:min-h-0"
                >
                  <LogoutIcon size={16} />
                  <span className="font-medium">Sign Out</span>
                </button>
              </div>
            </div>

            {isEditing ? (
              /* Edit Form - Mobile Optimized */
              <form onSubmit={handleUpdateProfile} className="space-y-4 sm:space-y-6">
                {/* Name Fields - Single column on mobile */}
                <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group">
                    <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2">
                      First Name
                    </label>
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 sm:px-4 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300 text-base touch-manipulation"
                      placeholder="First name"
                    />
                  </div>

                  <div className="group">
                    <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2">
                      Last Name
                    </label>
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-3 py-3 sm:px-4 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300 text-base touch-manipulation"
                      placeholder="Last name"
                    />
                  </div>
                </div>

                {/* Phone Number */}
                <div className="group">
                  <label htmlFor="phoneNumber" className="block text-sm font-semibold text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    id="phoneNumber"
                    name="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-3 sm:px-4 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300 text-base touch-manipulation"
                    placeholder="Phone number"
                  />
                </div>

                {/* Country */}
                <div className="group">
                  <label htmlFor="country" className="block text-sm font-semibold text-gray-700 mb-2">
                    Country
                  </label>
                  <input
                    id="country"
                    name="country"
                    type="text"
                    value={formData.country}
                    onChange={handleInputChange}
                    className="w-full px-3 py-3 sm:px-4 rounded-xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal transition-all duration-300 text-base touch-manipulation"
                    placeholder="Country"
                  />
                </div>

                {/* Update Button - Mobile Optimized */}
                <button
                  type="submit"
                  disabled={updating}
                  className={`
                    w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                    text-white font-bold py-3.5 sm:py-3 px-6 rounded-xl shadow-lg hover:shadow-xl
                    transform hover:scale-105 active:scale-95 transition-all duration-300
                    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                    touch-manipulation min-h-[48px] text-base
                  `}
                >
                  {updating ? 'Updating...' : 'Update Profile'}
                </button>
              </form>
            ) : (
              /* Profile Display - Mobile Optimized */
              <div className="space-y-4 sm:space-y-6">
                {/* Profile Information - Mobile-First Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-3 sm:gap-6">
                  <div className="flex items-center space-x-3 p-3 sm:p-4 bg-white/40 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-brand-accent-teal/10 rounded-lg flex items-center justify-center">
                      <MailIcon className="text-brand-accent-teal" size={18} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm text-gray-600 font-medium">Email</p>
                      <p className="font-medium text-gray-900 text-sm sm:text-base truncate">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 p-3 sm:p-4 bg-white/40 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-brand-accent-teal/10 rounded-lg flex items-center justify-center">
                      <UserIcon className="text-brand-accent-teal" size={18} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm text-gray-600 font-medium">Full Name</p>
                      <p className="font-medium text-gray-900 text-sm sm:text-base truncate">
                        {profile?.first_name} {profile?.last_name}
                      </p>
                    </div>
                  </div>

                  {profile?.phone_number && (
                    <div className="flex items-center space-x-3 p-3 sm:p-4 bg-white/40 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-brand-accent-teal/10 rounded-lg flex items-center justify-center">
                        <PhoneIcon className="text-brand-accent-teal" size={18} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm text-gray-600 font-medium">Phone</p>
                        <p className="font-medium text-gray-900 text-sm sm:text-base truncate">
                          {profile.country_code} {profile.phone_number}
                        </p>
                      </div>
                    </div>
                  )}

                  {profile?.country && (
                    <div className="flex items-center space-x-3 p-3 sm:p-4 bg-white/40 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-brand-accent-teal/10 rounded-lg flex items-center justify-center">
                        <GlobeAltIcon className="text-brand-accent-teal" size={18} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm text-gray-600 font-medium">Country</p>
                        <p className="font-medium text-gray-900 text-sm sm:text-base truncate">{profile.country}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Account Management - Mobile Optimized */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 py-4 px-4 sm:px-6 bg-gradient-to-r from-brand-accent-teal/10 to-transparent rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-brand-accent-teal/10 rounded-lg flex items-center justify-center">
                      <MapPinIcon className="text-brand-accent-teal" size={20} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-900 text-sm sm:text-base">Delivery Addresses ({addresses.length})</h4>
                      <p className="text-xs sm:text-sm text-gray-600">
                        {addresses.length === 0
                          ? "Add your delivery addresses"
                          : `Manage ${addresses.length} saved ${addresses.length === 1 ? 'address' : 'addresses'}`}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate('/addresses')}
                    className="w-full sm:w-auto px-4 py-2.5 sm:py-2 bg-brand-accent-teal text-white rounded-lg hover:bg-brand-accent-teal-darker transition-colors touch-manipulation min-h-[44px] sm:min-h-0 font-medium"
                  >
                    Manage Addresses
                  </button>
                </div>

                {/* Account Info - Mobile Optimized */}
                <div className="pt-4 sm:pt-6 border-t border-gray-200/50">
                  <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">Account Information</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 text-sm">
                    <div className="p-3 bg-white/30 rounded-lg">
                      <p className="text-gray-600 text-xs sm:text-sm font-medium">Member since</p>
                      <p className="font-medium text-gray-900 text-sm sm:text-base">
                        {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                    <div className="p-3 bg-white/30 rounded-lg">
                      <p className="text-gray-600 text-xs sm:text-sm font-medium">Last updated</p>
                      <p className="font-medium text-gray-900 text-sm sm:text-base">
                        {profile?.updated_at ? new Date(profile.updated_at).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
