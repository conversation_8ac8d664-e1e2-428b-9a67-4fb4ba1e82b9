import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { EyeIcon, EyeSlashIcon, MailIcon, LockClosedIcon, UserIcon, GlobeAltIcon } from '../common/Icon';
import PhoneInput from '../common/PhoneInput';
import CountrySelect from '../common/CountrySelect';
import Captcha from '../common/Captcha';

// Password strength checker
const checkPasswordStrength = (password: string) => {
  const requirements = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  const score = Object.values(requirements).filter(Boolean).length;

  let strength = 'weak';
  let color = 'bg-red-500';
  let textColor = 'text-red-600';

  if (score >= 4) {
    strength = 'strong';
    color = 'bg-green-500';
    textColor = 'text-green-600';
  } else if (score >= 3) {
    strength = 'medium';
    color = 'bg-yellow-500';
    textColor = 'text-yellow-600';
  }

  return { requirements, score, strength, color, textColor };
};

// Password Requirements Component
const PasswordRequirements: React.FC<{ password: string; isVisible: boolean }> = ({ password, isVisible }) => {
  const { requirements } = checkPasswordStrength(password);

  const requirementsList = [
    { key: 'length', text: 'At least 8 characters', met: requirements.length },
    { key: 'uppercase', text: 'One uppercase letter', met: requirements.uppercase },
    { key: 'lowercase', text: 'One lowercase letter', met: requirements.lowercase },
    { key: 'number', text: 'One number', met: requirements.number },
    { key: 'special', text: 'One special character (!@#$%^&*)', met: requirements.special },
  ];

  if (!isVisible) return null;

  return (
    <div className="mt-3 p-4 bg-gradient-to-br from-gray-50/90 to-white/80 backdrop-blur-sm rounded-xl border border-gray-200/50 transition-all duration-500 shadow-lg hover:shadow-xl">
      <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
        <div className="w-2 h-2 bg-brand-accent-teal rounded-full mr-2"></div>
        Password Requirements:
      </h4>
      <div className="space-y-3">
        {requirementsList.map((req, index) => (
          <div
            key={req.key}
            className={`flex items-center space-x-3 requirement-slide-in transition-all duration-300 ${
              req.met ? 'transform scale-105' : ''
            }`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className={`w-5 h-5 rounded-full flex items-center justify-center transition-all duration-500 shadow-md ${
              req.met
                ? 'bg-gradient-to-r from-green-400 to-green-600 checkmark-bounce shadow-green-200'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}>
              {req.met && (
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <span className={`text-sm transition-all duration-300 ${
              req.met
                ? 'text-green-700 font-semibold'
                : 'text-gray-600 hover:text-gray-700'
            }`}>
              {req.text}
            </span>
          </div>
        ))}
      </div>

      {/* Progress indicator */}
      <div className="mt-4 pt-3 border-t border-gray-200/50">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600">Progress</span>
          <span className="font-semibold text-brand-accent-teal">
            {Object.values(requirements).filter(Boolean).length}/5
          </span>
        </div>
        <div className="mt-1 w-full bg-gray-200 rounded-full h-1.5 overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-brand-accent-teal to-green-500 password-strength-grow rounded-full"
            style={{ width: `${(Object.values(requirements).filter(Boolean).length / 5) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
};

// Password Strength Indicator
const PasswordStrengthIndicator: React.FC<{ password: string }> = ({ password }) => {
  const { score, strength, color, textColor } = checkPasswordStrength(password);

  if (!password) return null;

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between mb-1">
        <span className="text-xs font-medium text-gray-600">Password Strength</span>
        <span className={`text-xs font-bold capitalize ${textColor} transition-all duration-300 transform`}>
          {strength}
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden shadow-inner">
        <div
          className={`h-full ${color} password-strength-grow rounded-full relative overflow-hidden`}
          style={{ width: `${(score / 5) * 100}%` }}
        >
          {/* Shimmer effect for strong passwords */}
          {strength === 'strong' && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
          )}
        </div>
      </div>
      {/* Strength indicator dots */}
      <div className="flex justify-center mt-1 space-x-1">
        {[1, 2, 3, 4, 5].map((level) => (
          <div
            key={level}
            className={`w-1.5 h-1.5 rounded-full transition-all duration-300 ${
              level <= score
                ? color.replace('bg-', 'bg-') + ' scale-110'
                : 'bg-gray-300 scale-100'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

// Add notification popup styles
const NotificationPopup: React.FC<{ message: string; onClose: () => void }> = ({ message, onClose }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/50">
      <div className="bg-white rounded-3xl p-8 max-w-md w-full mx-4 relative transform transition-all scale-100 animate-fade-in shadow-2xl border border-gray-100">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <MailIcon className="h-6 w-6 text-green-600" />
          </div>
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Email Verification Required</h3>
          <p className="text-sm text-gray-500 mb-6">{message}</p>
          <button
            onClick={onClose}
            className="w-full inline-flex justify-center rounded-2xl border border-transparent shadow-sm px-4 py-3 bg-brand-main-red text-base font-medium text-white hover:bg-brand-main-red-darker focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-main-red sm:text-sm transition-all duration-300"
          >
            Got it!
          </button>
        </div>
      </div>
    </div>
  );
};

const SignupPage: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    countryCode: '+1',
    country: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [showPasswordRequirements, setShowPasswordRequirements] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string>('');
  const [captchaError, setCaptchaError] = useState<string>('');

  const { signUp, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    setIsVisible(true);
    // Redirect if already logged in
    if (user) {
      navigate('/profile');
    }
  }, [user, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    const { requirements, score } = checkPasswordStrength(formData.password);

    if (!requirements.length) {
      setError('Password must be at least 8 characters long');
      return false;
    }
    if (!requirements.uppercase) {
      setError('Password must contain at least one uppercase letter');
      return false;
    }
    if (!requirements.lowercase) {
      setError('Password must contain at least one lowercase letter');
      return false;
    }
    if (!requirements.number) {
      setError('Password must contain at least one number');
      return false;
    }
    if (!requirements.special) {
      setError('Password must contain at least one special character (!@#$%^&*)');
      return false;
    }

    if (score < 4) {
      setError('Password is too weak. Please meet at least 4 out of 5 requirements');
      return false;
    }

    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      setError('First name and last name are required');
      return false;
    }

    // Captcha validation
    if (!captchaToken) {
      setError('Please complete the security verification');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    const userData = {
      first_name: formData.firstName.trim(),
      last_name: formData.lastName.trim(),
      phone_number: formData.phoneNumber.trim() || undefined,
      country_code: formData.countryCode,
      country: formData.country || undefined,
    };

    const { error } = await signUp(formData.email, formData.password, userData, captchaToken);

    if (error) {
      setError(error.message);
      setLoading(false);
    } else {
      // Show notification popup
      setShowNotification(true);
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-page-bg-light via-gray-100 to-gray-200 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-brand-accent-teal/10 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-brand-main-red/10 rounded-full blur-3xl animate-pulse-slow animation-delay-700"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white/20 rounded-full blur-2xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-2xl mx-auto">
        {/* Logo and Header */}
        <div className={`text-center mb-8 transition-all duration-1000 ease-out ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          <img 
            src="/assets/logo.png" 
            alt="Le Prestine Logo" 
            className="h-16 w-auto mx-auto mb-6"
          />
          <h2 className="text-3xl font-bold text-gray-900 font-serif">
            Join Le Prestine
          </h2>
          <p className="mt-2 text-gray-600">
            Create your account to get started
          </p>
        </div>

        {/* Signup Form */}
        <div className={`
          bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8
          transition-all duration-1200 ease-out
          ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}
        `} style={{ transitionDelay: '200ms' }}>
          {/* Glass Morphism Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-brand-accent-teal/5 rounded-3xl pointer-events-none"></div>

          <form onSubmit={handleSubmit} className="space-y-6 relative">
            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl text-sm">
                {error}
              </div>
            )}

            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group">
                <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  First Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <UserIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="First name"
                  />
                </div>
              </div>

              <div className="group">
                <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Last Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <UserIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                    placeholder="Last name"
                  />
                </div>
              </div>
            </div>

            {/* Email Field */}
            <div className="group">
              <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                Email Address *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <MailIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-12 pr-4 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80 transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Phone Number Field */}
            <PhoneInput
              value={formData.phoneNumber}
              onChange={(value) => setFormData(prev => ({ ...prev, phoneNumber: value }))}
              countryCode={formData.countryCode}
              onCountryChange={(code) => setFormData(prev => ({ ...prev, countryCode: code }))}
              placeholder="Phone number"
              className="group"
            />

            {/* Country Field */}
              <CountrySelect 
                value={formData.country}
                onChange={(country, countryCode) => {
                  setFormData(prev => ({
                    ...prev,
                    country,
                    countryCode,
                    phoneNumber: '' // Reset phone number when country changes
                  }));
                }}
                required
                placeholder="Select your country"
                className="group"
              />

            {/* Password Fields */}
            <div className="space-y-6">
              <div className="group">
                <label htmlFor="password" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <LockClosedIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    onFocus={() => {
                      setPasswordFocused(true);
                      setShowPasswordRequirements(true);
                    }}
                    onBlur={() => {
                      setPasswordFocused(false);
                      // Keep requirements visible if password has content
                      if (!formData.password) {
                        setShowPasswordRequirements(false);
                      }
                    }}
                    className={`w-full pl-12 pr-12 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70 ${
                      passwordFocused
                        ? 'border-brand-accent-teal focus:ring-2 focus:ring-brand-accent-teal/50 focus:bg-white/80 password-focus-ring'
                        : 'border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80'
                    }`}
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    ) : (
                      <EyeIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    )}
                  </button>
                </div>

                {/* Password Strength Indicator */}
                <PasswordStrengthIndicator password={formData.password} />

                {/* Password Requirements */}
                <PasswordRequirements
                  password={formData.password}
                  isVisible={showPasswordRequirements || passwordFocused || formData.password.length > 0}
                />
              </div>

              <div className="group">
                <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-700 mb-2 transition-colors group-focus-within:text-brand-accent-teal">
                  Confirm Password *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <LockClosedIcon className="text-gray-400 group-focus-within:text-brand-accent-teal transition-colors" size={20} />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full pl-12 pr-12 py-4 rounded-2xl bg-white/60 backdrop-blur-sm border transition-all duration-300 placeholder-gray-400 text-gray-800 shadow-lg hover:shadow-xl hover:bg-white/70 ${
                      formData.confirmPassword && formData.password !== formData.confirmPassword
                        ? 'border-red-300 focus:ring-2 focus:ring-red-500/50 focus:border-red-500 focus:bg-white/80'
                        : formData.confirmPassword && formData.password === formData.confirmPassword
                        ? 'border-green-300 focus:ring-2 focus:ring-green-500/50 focus:border-green-500 focus:bg-white/80'
                        : 'border-gray-200/50 focus:ring-2 focus:ring-brand-accent-teal/50 focus:border-brand-accent-teal focus:bg-white/80'
                    }`}
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    ) : (
                      <EyeIcon className="text-gray-400 hover:text-gray-600 transition-colors" size={20} />
                    )}
                  </button>
                </div>

                {/* Password Match Indicator */}
                {formData.confirmPassword && (
                  <div className="mt-2 flex items-center space-x-2">
                    {formData.password === formData.confirmPassword ? (
                      <>
                        <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                          <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm text-green-600 font-medium">Passwords match</span>
                      </>
                    ) : (
                      <>
                        <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                          <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-sm text-red-600 font-medium">Passwords don't match</span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Captcha */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Security Verification
              </label>
              <Captcha
                onVerify={(token) => {
                  setCaptchaToken(token);
                  setCaptchaError('');
                }}
                onError={(error) => {
                  setCaptchaError(error);
                  setCaptchaToken('');
                }}
                onExpire={() => {
                  setCaptchaToken('');
                  setCaptchaError('Captcha expired. Please verify again.');
                }}
                className="w-full"
                theme="light"
                size="normal"
              />
              {captchaError && (
                <p className="text-sm text-red-600">{captchaError}</p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className={`
                group relative w-full overflow-hidden bg-gradient-to-r from-brand-main-red to-brand-main-red-darker
                text-white font-bold py-4 px-8 rounded-2xl shadow-2xl hover:shadow-3xl
                transform hover:scale-105 active:scale-95 focus:outline-none focus:ring-4 focus:ring-brand-main-red/30
                transition-all duration-500 ease-out text-lg border border-white/20
                disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
              `}
            >
              {/* Shimmer Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
              
              <span className="relative">
                {loading ? 'Creating Account...' : 'Create Account'}
              </span>
            </button>

            {/* Sign In Link */}
            <div className="text-center">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link 
                  to="/login" 
                  className="font-semibold text-brand-accent-teal hover:text-brand-accent-teal-darker transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
      
      {/* Notification Popup */}
      {showNotification && (
        <NotificationPopup
          message="We've sent a verification link to your email address. Please check your inbox and click the link to verify your account."
          onClose={() => {
            setShowNotification(false);
            navigate('/login', {
              state: { message: 'Account created successfully! Your data has been saved. Please check your email to verify your account.' }
            });
          }}
        />
      )}
    </div>
  );
};

export default SignupPage;
