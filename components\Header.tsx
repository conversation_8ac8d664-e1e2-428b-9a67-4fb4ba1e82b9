import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { UserIcon, ShoppingCartIcon, LogoutIcon, MenuIcon, XIcon } from './common/Icon';
import styles from '../src/styles/animations.module.css';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, profile, signOut } = useAuth();
  const { cartSummary } = useCart();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isContactUsInView, setIsContactUsInView] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Store', path: '/store' },
    { name: 'About Us', path: '/about' },
    { name: 'Contact Us', path: 'contact' },
  ];

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Function to handle Contact Us navigation with smooth scrolling
  const handleContactUsNavigation = () => {
    if (location.pathname !== '/') {
      navigate('/');
      setTimeout(() => {
        const contactSection = document.getElementById('contact-us');
        if (contactSection) {
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    } else {
      const contactSection = document.getElementById('contact-us');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }
  };

  // Handle user sign out
  const handleSignOut = async () => {
    const { error } = await signOut();
    if (!error) {
      setShowUserDropdown(false);
      navigate('/');
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.user-dropdown')) {
        setShowUserDropdown(false);
      }
    };

    if (showUserDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showUserDropdown]);

  // Intersection Observer for ContactUs section visibility
  useEffect(() => {
    if (location.pathname !== '/') {
      setIsContactUsInView(false);
      return;
    }

    const contactSection = document.getElementById('contact-us');
    if (!contactSection) {
      setIsContactUsInView(false);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsContactUsInView(entry.isIntersecting);
      },
      {
        threshold: 0.5,
        rootMargin: '-80px 0px -80px 0px',
      }
    );

    observer.observe(contactSection);
    return () => observer.disconnect();
  }, [location.pathname]);

  // Handle scroll effects
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      <header
        className={`text-white sticky top-0 z-50 transition-all duration-500 ease-in-out ${
          isScrolled
            ? 'bg-brand-accent-teal/20 backdrop-blur-xl shadow-2xl'
            : 'bg-brand-accent-teal shadow-lg'
        }`}
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16 md:h-20">
            {/* Logo */}
            <div className="flex items-center">
              <img src="/assets/logo.png" alt="Le Pristine Logo" className="h-8 sm:h-10 md:h-12 w-auto transition-all duration-300" />
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-6 lg:space-x-8">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (item.path === 'contact') {
                      handleContactUsNavigation();
                    } else {
                      navigate(item.path);
                    }
                  }}
                  className={`font-medium transition-colors duration-200 ease-in-out py-2
                    ${(() => {
                      if (item.path === 'contact') {
                        return isContactUsInView
                          ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                          : 'text-white hover:text-brand-main-red-darker';
                      }
                      if (item.path === '/') {
                        return (location.pathname === '/' && !isContactUsInView)
                          ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                          : 'text-white hover:text-brand-main-red-darker';
                      }
                      return location.pathname === item.path
                        ? 'text-brand-main-red font-semibold border-b-2 border-brand-main-red'
                        : 'text-white hover:text-brand-main-red-darker';
                    })()}`}
                >
                  {item.name}
                </a>
              ))}
            </nav>

            {/* Icons */}
            <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
              {/* Mobile Menu Button */}
              <button
                className="md:hidden relative w-10 h-10 flex items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-all duration-300"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
              >
                {isMobileMenuOpen ? <XIcon size={20} /> : <MenuIcon size={20} />}
              </button>

              {/* User Account Button */}
              <div className="relative user-dropdown">
                <button
                  onClick={() => {
                    if (user) {
                      setShowUserDropdown(!showUserDropdown);
                    } else {
                      navigate('/login');
                    }
                  }}
                  className="p-2.5 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                  aria-label={user ? "User account menu" : "Sign in"}
                >
                  <UserIcon size={18} />
                </button>

                {/* User Dropdown Menu */}
                {user && showUserDropdown && (
                  <div className={`absolute right-0 mt-3 w-72 sm:w-72 bg-white/40 backdrop-blur-md rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.12)] border border-white/20 py-3 z-50 transition-all duration-300 ${styles.fadeIn}`}>
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/50 to-white/30 pointer-events-none"></div>
                    
                    {/* User Info */}
                    <div className="px-5 py-3 border-b border-white/20 relative">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-brand-accent-teal via-brand-accent-teal-darker to-brand-accent-teal rounded-xl flex items-center justify-center shadow-lg transform transition-transform hover:scale-105">
                          <UserIcon className="text-white" size={20} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 mb-0.5 truncate">
                            {profile?.first_name} {profile?.last_name}
                          </p>
                          <p className="text-sm text-gray-600 truncate">{user.email}</p>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="py-2">
                      <button
                        onClick={() => {
                          navigate('/profile');
                          setShowUserDropdown(false);
                        }}
                        className="w-full text-left px-5 py-2.5 text-gray-700 hover:bg-white/50 transition-all duration-200 flex items-center space-x-3 group"
                      >
                        <div className="p-1.5 rounded-lg bg-gray-100/80 group-hover:bg-white/80 transition-colors">
                          <UserIcon size={16} className="text-gray-600" />
                        </div>
                        <span className="font-medium">My Profile</span>
                      </button>

                      <button
                        onClick={handleSignOut}
                        className="w-full text-left px-5 py-2.5 text-red-600 hover:bg-red-50/50 transition-all duration-200 flex items-center space-x-3 group mt-1"
                      >
                        <div className="p-1.5 rounded-lg bg-red-100/80 group-hover:bg-red-100 transition-colors">
                          <LogoutIcon size={16} className="text-red-600" />
                        </div>
                        <span className="font-medium">Sign Out</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Cart Button */}
              <button
                onClick={() => navigate('/cart')}
                className="relative p-2.5 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                aria-label={`View shopping cart with ${cartSummary.itemCount} items`}
              >
                <ShoppingCartIcon size={18} />
                {cartSummary.itemCount > 0 && (
                  <span className="absolute -top-1.5 -right-1.5 bg-brand-main-red text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-semibold">
                    {cartSummary.itemCount > 99 ? '99+' : cartSummary.itemCount}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation Menu */}
      <div
        className={`fixed inset-0 bg-black/50 backdrop-blur-sm md:hidden transition-opacity duration-300 z-40
          ${isMobileMenuOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
        onClick={() => setIsMobileMenuOpen(false)}
      >
        <div
          className={`absolute right-0 top-0 h-full w-64 bg-brand-accent-teal/95 backdrop-blur-lg shadow-xl 
            transform transition-transform duration-300 ease-out ${isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}`}
          onClick={e => e.stopPropagation()}
        >
          <div className="flex flex-col h-full pt-20 pb-6 px-4">
            <nav className="flex-1">
              <div className="flex flex-col space-y-2">
                {navItems.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      if (item.path === 'contact') {
                        handleContactUsNavigation();
                      } else {
                        navigate(item.path);
                      }
                    }}
                    className={`w-full text-left font-medium transition-all duration-200 py-3 px-4 rounded-lg
                      ${(() => {
                        if (item.path === 'contact') {
                          return isContactUsInView
                            ? 'bg-white/20 text-white font-semibold'
                            : 'text-white hover:bg-white/10';
                        }
                        if (item.path === '/') {
                          return (location.pathname === '/' && !isContactUsInView)
                            ? 'bg-white/20 text-white font-semibold'
                            : 'text-white hover:bg-white/10';
                        }
                        return location.pathname === item.path
                          ? 'bg-white/20 text-white font-semibold'
                          : 'text-white hover:bg-white/10';
                      })()}`}
                  >
                    {item.name}
                  </button>
                ))}
              </div>
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default Header;
