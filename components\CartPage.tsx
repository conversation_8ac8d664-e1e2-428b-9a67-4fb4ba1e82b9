import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { TrashIcon, PlusIcon, MinusIcon, ShoppingBagIcon, MapPinIcon, ChevronDownIcon } from './common/Icon';
import { useAddress } from '../contexts/AddressContext';
import AddressList from './addresses/AddressList';

interface GroupedCartItem {
  id: string; // Use the first cart item's ID as the group ID
  product: any;
  variant_id?: string;
  cartItemIds: string[]; // Array of all cart item IDs in this group
  totalQuantity: number;
  index?: number;
}

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cartItems, cartSummary, loading, removeFromCart, updateQuantity } = useCart();
  const { addresses } = useAddress();
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [processingItems, setProcessingItems] = useState<Set<string>>(new Set());
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);

  // Mobile enhancement states
  const [isStickyBottomVisible, setIsStickyBottomVisible] = useState(true);
  const [isOrderSummaryInView, setIsOrderSummaryInView] = useState(false);
  const orderSummaryRef = useRef<HTMLDivElement>(null);

  // WhatsApp guide modal states
  const [showWhatsAppGuide, setShowWhatsAppGuide] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [orderMessage, setOrderMessage] = useState('');

  // Device detection helper function
  const isMobileOrTablet = () => {
    return window.innerWidth < 1024;
  };

  // Get total steps based on device
  const getTotalSteps = () => {
    return isMobileOrTablet() ? 2 : 3;
  };

  // Helper function to get variant image path
  const getVariantImage = (productName: string, variantId: string) => {
    // Handle hand wash variants specifically
    if (productName.includes('Hand Wash')) {
      switch (variantId) {
        case 'green-apple': return '/assets/products/handwash-1.jpg';
        case 'strawberry': return '/assets/products/handwash-2.jpg';
        case 'lavender': return '/assets/products/handwash-3.jpg';
        default: return '/assets/products/handwash-three-flavors.jpg';
      }
    }
    // Add mappings for other products as needed
    return '';
  };

  // Function to generate order message
  const generateOrderMessage = () => {
    if (!selectedAddressId) return '';

    // Create clean WhatsApp message without problematic Unicode characters
    let message = "*NEW ORDER REQUEST*\n\n";
    message += "================================\n";
    message += "         ORDER SUMMARY          \n";
    message += "================================\n\n";

    // Add delivery address with simple formatting
    const deliveryAddress = addresses.find(addr => addr.id === selectedAddressId);
    if (deliveryAddress) {
      message += "*DELIVERY ADDRESS:*\n";
      message += "--------------------------------\n";
      message += `*Name:* ${deliveryAddress.full_name}\n`;
      message += `*Phone:* ${deliveryAddress.phone_number}\n`;
      message += `*Address:* ${deliveryAddress.street_address}\n`;
      if (deliveryAddress.apartment) {
        message += `*Apt/Unit:* ${deliveryAddress.apartment}\n`;
      }
      message += `*City:* ${deliveryAddress.city}\n`;
      message += `*State:* ${deliveryAddress.state}\n`;
      message += `*Postal Code:* ${deliveryAddress.postal_code}\n`;
      message += `*Country:* ${deliveryAddress.country}\n`;
      message += "--------------------------------\n\n";
    }

    message += "*ORDERED ITEMS:*\n";
    message += "--------------------------------\n";

    // Group cart items properly (same logic as order summary)
    const groupedItems = Object.values(cartItems.reduce((acc: any, item) => {
      const product = item.product;
      if (!product) return acc;

      const key = `${product.id}-${item.variant_id || 'default'}`;
      if (!acc[key]) {
        acc[key] = {
          product,
          variant_id: item.variant_id,
          totalQuantity: 0,
          totalPrice: 0
        };
      }
      acc[key].totalQuantity += item.quantity;
      acc[key].totalPrice += product.price * item.quantity;
      return acc;
    }, {}));

    groupedItems.forEach((groupedItem: any, index) => {
      const product = groupedItem.product;
      let itemText = `${index + 1}. *${product.name}*`;

      if (groupedItem.variant_id && product.variants) {
        const variant = product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id);
        if (variant) {
          itemText += ` (${variant.name})`;
        }
      }

      itemText += `\n   Qty: ${groupedItem.totalQuantity} x AED ${product.price.toFixed(2)}`;
      itemText += `\n   Subtotal: AED ${groupedItem.totalPrice.toFixed(2)}`;

      if (product.original_price && product.discount) {
        const savings = (product.original_price - product.price) * groupedItem.totalQuantity;
        itemText += `\n   You Save: AED ${savings.toFixed(2)} (${product.discount})`;
      }

      itemText += "\n\n";
      message += itemText;
    });

    message += "--------------------------------\n";

    // Add pricing breakdown with simple formatting
    message += "*PRICING BREAKDOWN:*\n";

    if (cartSummary.totalSavings && cartSummary.totalSavings > 0) {
      message += `Original Total: AED ${cartSummary.originalTotal?.toFixed(2)}\n`;
      message += `Total Savings: -AED ${cartSummary.totalSavings.toFixed(2)}\n`;
      message += "--------------------------------\n";
    }

    message += `*FINAL TOTAL: AED ${cartSummary.total.toFixed(2)}*\n\n`;

    if (cartSummary.totalSavings && cartSummary.totalSavings > 0) {
      message += `Total Savings: AED ${cartSummary.totalSavings.toFixed(2)} on this order!\n\n`;
    }

    message += "*Please confirm this order and let me know the payment details.*\n\n";
    message += "Thank you!";

    return message;
  };

  // Helper function to group cart items by product and variant
  const groupCartItems = (items: typeof cartItems): GroupedCartItem[] => {
    const groups: { [key: string]: GroupedCartItem } = {};

    // Sort items by ID to ensure consistent grouping
    const sortedItems = [...items].sort((a, b) => a.id.localeCompare(b.id));

    sortedItems.forEach((item) => {
      if (!item.product) return;

      const key = `${item.product.id}-${item.variant_id || 'default'}`;

      if (!groups[key]) {
        groups[key] = {
          id: item.id,
          product: item.product,
          variant_id: item.variant_id,
          cartItemIds: [item.id],
          totalQuantity: item.quantity,
        };
      } else {
        groups[key].cartItemIds.push(item.id);
        groups[key].totalQuantity += item.quantity;
      }
    });

    return Object.values(groups);
  };

  // Redirect if not logged in
  useEffect(() => {
    if (!user && !loading) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  // Animate items on load
  useEffect(() => {
    const groupedItems = groupCartItems(cartItems);
    const timer = setTimeout(() => {
      groupedItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems(prev => new Set([...prev, index]));
        }, index * 100);
      });
    }, 200);

    return () => clearTimeout(timer);
  }, [cartItems]);

  // Intersection Observer for Order Summary visibility (Mobile Enhancement)
  useEffect(() => {
    if (!orderSummaryRef.current || cartItems.length === 0) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsOrderSummaryInView(entry.isIntersecting);
        // Hide sticky bottom when order summary is in view on mobile/tablet
        if (window.innerWidth < 1024) {
          setIsStickyBottomVisible(!entry.isIntersecting);
        }
      },
      {
        threshold: 0.3,
        rootMargin: '-80px 0px -80px 0px',
      }
    );

    observer.observe(orderSummaryRef.current);
    return () => observer.disconnect();
  }, [cartItems.length]);

  // Handle window resize to ensure proper sticky bottom behavior
  useEffect(() => {
    const handleResize = () => {
      // Reset sticky bottom visibility on desktop
      if (window.innerWidth >= 1024) {
        setIsStickyBottomVisible(false);
      } else if (cartItems.length > 0) {
        setIsStickyBottomVisible(!isOrderSummaryInView);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on mount

    return () => window.removeEventListener('resize', handleResize);
  }, [cartItems.length, isOrderSummaryInView]);

  // Handle smooth scroll to order summary
  const handleScrollToOrderSummary = () => {
    if (orderSummaryRef.current) {
      orderSummaryRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  // Handlers for grouped items
  const handleGroupedQuantityChange = async (groupedItem: GroupedCartItem, change: number) => {
    const newQuantity = groupedItem.totalQuantity + change;

    if (newQuantity <= 0) {
      const allItemIds = groupedItem.cartItemIds;
      setProcessingItems(prev => new Set([...prev, ...allItemIds]));

      try {
        await Promise.all(allItemIds.map(id => removeFromCart(id)));
      } finally {
        setProcessingItems(prev => {
          const newSet = new Set(prev);
          allItemIds.forEach(id => newSet.delete(id));
          return newSet;
        });
      }
      return;
    }

    // Only update the first item and handle duplicates after success
    const [firstItemId, ...otherItemIds] = groupedItem.cartItemIds;
    
    // Only mark the first item as processing initially
    setProcessingItems(prev => new Set([...prev, firstItemId]));

    try {
      // Update quantity of the first item
      const result = await updateQuantity(firstItemId, newQuantity);
      
      if (result.success && otherItemIds.length > 0) {
        // Mark remaining items as processing only after successful quantity update
        setProcessingItems(prev => new Set([...prev, ...otherItemIds]));
        // Remove duplicates in parallel
        await Promise.all(otherItemIds.map(id => removeFromCart(id)));
      }
    } finally {
      setProcessingItems(prev => {
        const newSet = new Set(prev);
        groupedItem.cartItemIds.forEach(id => newSet.delete(id));
        return newSet;
      });
    }
  };

  const handleRemoveGroup = async (groupedItem: GroupedCartItem) => {
    const allItemIds = groupedItem.cartItemIds;
    setProcessingItems(prev => new Set([...prev, ...allItemIds]));

    for (const itemId of allItemIds) {
      await removeFromCart(itemId);
    }

    setProcessingItems(prev => {
      const newSet = new Set(prev);
      allItemIds.forEach(id => newSet.delete(id));
      return newSet;
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-accent-teal mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your cart...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen font-sans relative overflow-hidden">
      {/* Background remains the same... */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-page-bg-light to-gray-100">
        {/* Previous background patterns remain unchanged */}
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-12 pb-24 lg:pb-12">
        {/* Header remains the same... */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Shopping Cart
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Review your selected items and proceed to checkout
          </p>
        </div>

        {cartItems.length === 0 ? (
          // Empty Cart State
          <div className="text-center py-16">
            <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/50 p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gradient-to-br from-brand-accent-teal/20 to-brand-main-red/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <ShoppingBagIcon size={40} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h3>
              <p className="text-gray-600 mb-8">
                Discover our amazing products and add them to your cart
              </p>
              <button
                onClick={() => navigate('/store')}
                className="bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                Continue Shopping
              </button>
            </div>
          </div>
        ) : (
          // Updated Layout to include Address Selection
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Cart Items List - Made slightly smaller */}
            <div className="lg:col-span-7 space-y-3 sm:space-y-4 lg:space-y-6">
              {groupCartItems(cartItems).map((groupedItem, index) => {
                const product = groupedItem.product;
                if (!product) return null;

                const isVisible = visibleItems.has(index);
                const isProcessing = groupedItem.cartItemIds.some(id => processingItems.has(id));

                return (
                  <div
                    key={`${product.id}-${groupedItem.variant_id || 'default'}`}
                    className={`bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 p-3 sm:p-4 lg:p-6 transition-all duration-700 ${
                      isVisible ? 'opacity-100 transform-none' : 'opacity-0 translate-y-8'
                    } ${isProcessing ? 'opacity-50' : ''}`}
                  >
                    <div className="flex gap-3 sm:gap-4 lg:gap-6">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg sm:rounded-xl overflow-hidden">
                          {product.name.includes('Hand Wash') && groupedItem.variant_id ? (
                            <img
                              src={getVariantImage(product.name, groupedItem.variant_id)}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <img
                              src={product.image_url}
                              alt={product.name}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-grow">
                        <div className="flex justify-between items-start mb-2 sm:mb-3 lg:mb-4">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 mb-1 sm:mb-2 leading-tight">
                              {product.name}
                              {groupedItem.variant_id && product.variants && (
                                <span className="text-brand-accent-teal block text-sm sm:text-base font-normal mt-1">
                                  {product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id)?.name}
                                </span>
                              )}
                            </h3>
                            <p className="text-gray-600 text-xs sm:text-sm mb-1 sm:mb-2">{product.category}</p>
                            {product.original_price && product.discount ? (
                              <div className="flex flex-col">
                                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                                  <span className="bg-brand-main-red text-white text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full font-bold">
                                    {product.discount}
                                  </span>
                                  <span className="text-xs sm:text-sm text-gray-500 line-through">
                                    AED {product.original_price.toFixed(2)}
                                  </span>
                                </div>
                                <p className="text-brand-accent-teal font-semibold text-base sm:text-lg">
                                  AED {product.price.toFixed(2)}
                                </p>
                                <span className="text-xs text-brand-accent-teal font-medium">
                                  Save AED {(product.original_price - product.price).toFixed(2)} each
                                </span>
                              </div>
                            ) : (
                              <p className="text-brand-accent-teal font-semibold text-base sm:text-lg">
                                AED {product.price.toFixed(2)}
                              </p>
                            )}
                          </div>
                          <button
                            onClick={() => handleRemoveGroup(groupedItem)}
                            disabled={isProcessing}
                            className="p-1.5 sm:p-2 text-red-500 hover:bg-red-50 rounded-full transition-colors duration-200 disabled:opacity-50 flex-shrink-0"
                            aria-label="Remove item"
                          >
                            <TrashIcon size={16} className="sm:w-5 sm:h-5" />
                          </button>
                        </div>

                        {/* Quantity Controls */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                          <div className="flex items-center space-x-2 sm:space-x-3">
                            <span className="text-gray-600 font-medium text-sm sm:text-base">Qty:</span>
                            <div className="flex items-center space-x-1 sm:space-x-2">
                              <button
                                onClick={() => handleGroupedQuantityChange(groupedItem, -1)}
                                disabled={isProcessing || groupedItem.totalQuantity <= 1}
                                className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <MinusIcon size={14} className="sm:w-4 sm:h-4" />
                              </button>
                              <span className="w-8 sm:w-12 text-center font-semibold text-base sm:text-lg">
                                {groupedItem.totalQuantity}
                              </span>
                              <button
                                onClick={() => handleGroupedQuantityChange(groupedItem, 1)}
                                disabled={isProcessing}
                                className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 disabled:opacity-50"
                              >
                                <PlusIcon size={14} className="sm:w-4 sm:h-4" />
                              </button>
                            </div>
                          </div>
                          <div className="text-right sm:text-right">
                            {product.original_price && (
                              <p className="text-xs sm:text-sm text-gray-500 line-through">
                                AED {(product.original_price * groupedItem.totalQuantity).toFixed(2)}
                              </p>
                            )}
                            <p className="text-base sm:text-lg font-bold text-gray-900">
                              AED {(product.price * groupedItem.totalQuantity).toFixed(2)}
                            </p>
                            {product.original_price && (
                              <p className="text-xs sm:text-sm text-brand-accent-teal font-semibold">
                                Save AED {((product.original_price - product.price) * groupedItem.totalQuantity).toFixed(2)}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Address Selection - New section */}
            <div className="lg:col-span-5">
              {/* Order Summary */}
              <div ref={orderSummaryRef} className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 sticky top-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h3>
                
                <div className="space-y-4 mb-6">
                  {/* Show original total if there are savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Original Total</span>
                      <span className="text-gray-500 line-through">AED {cartSummary.originalTotal?.toFixed(2)}</span>
                    </div>
                  )}

                  {/* Show grouped products with total quantities */}
                  <div className="space-y-2">
                    {Object.values(cartItems.reduce((acc: any, item) => {
                      const product = item.product;
                      if (!product) return acc;
                      
                      const key = `${product.id}-${item.variant_id || 'default'}`;
                      if (!acc[key]) {
                        acc[key] = {
                          product,
                          variant_id: item.variant_id,
                          totalQuantity: 0,
                          totalPrice: 0
                        };
                      }
                      acc[key].totalQuantity += item.quantity;
                      acc[key].totalPrice += product.price * item.quantity;
                      return acc;
                    }, {})).map((groupedItem: any) => (
                      <div key={`${groupedItem.product.id}-${groupedItem.variant_id || 'default'}`} 
                           className="flex justify-between items-start text-sm gap-2">
                        <span className="text-gray-600 flex-1 leading-relaxed">
                          <span className="block sm:inline">{groupedItem.product.name}</span>
                          {groupedItem.variant_id && groupedItem.product.variants && (
                            <span className="block text-brand-accent-teal text-sm">
                              {groupedItem.product.variants.find((v: { id: string; name: string }) => v.id === groupedItem.variant_id)?.name}
                            </span>
                          )}
                          <span className="block sm:inline sm:ml-1">x {groupedItem.totalQuantity}</span>
                        </span>
                        <span className="font-semibold text-right flex-shrink-0">
                          AED {groupedItem.totalPrice.toFixed(2)}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Show total savings */}
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-brand-accent-teal font-semibold">Total Savings</span>
                      <span className="text-brand-accent-teal font-bold">-AED {cartSummary.totalSavings.toFixed(2)}</span>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <div className="flex justify-between text-xl font-bold">
                      <span>Total</span>
                      <span className="text-brand-main-red">AED {cartSummary.total.toFixed(2)}</span>
                    </div>

                    {/* Highlight total savings */}
                    {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                      <div className="mt-2 text-center">
                        <span className="bg-brand-accent-teal text-white px-4 py-2 rounded-full text-sm font-bold">
                          🎉 You saved AED {cartSummary.totalSavings.toFixed(2)} total!
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Updated checkout button section */}
                <div className="space-y-3">
                  <button
                    className={`w-full bg-gradient-to-r from-brand-main-red to-brand-main-red-darker text-white py-4 rounded-full font-bold text-lg transition-all duration-300 ${
                      selectedAddressId ? 'hover:shadow-lg transform hover:scale-105' : 'opacity-50 cursor-not-allowed'
                    }`}
                    disabled={!selectedAddressId}
                    onClick={() => {
                      if (!selectedAddressId) {
                        return;
                      }

                      const message = generateOrderMessage();

                      // Copy message to clipboard first
                      navigator.clipboard.writeText(message).then(() => {
                        console.log('Order message copied to clipboard');
                      }).catch(err => {
                        console.error('Failed to copy message to clipboard:', err);
                      });

                      const encodedMessage = encodeURIComponent(message);

                      // Use wa.me URL - this should work with WhatsApp Desktop on Windows 11
                      const whatsappUrl = `https://wa.me/************?text=${encodedMessage}`;

                      // Open the URL - Windows 11 should handle this properly
                      const newWindow = window.open(whatsappUrl, '_blank');

                      // Show user a helpful message
                      if (newWindow) {
                        // Small delay to show a toast notification
                        setTimeout(() => {
                          alert('📋 Order details copied to clipboard!\n\nIf WhatsApp doesn\'t open automatically, you can paste the message manually.');
                        }, 500);
                      }
                    }}
                  >
                    Proceed to Checkout via WhatsApp
                  </button>

                  <button
                    className={`w-full bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white py-3 rounded-full font-semibold text-base transition-all duration-300 ${
                      selectedAddressId ? 'hover:shadow-lg transform hover:scale-105' : 'opacity-50 cursor-not-allowed'
                    }`}
                    disabled={!selectedAddressId}
                    onClick={() => {
                      if (!selectedAddressId) {
                        return;
                      }
                      const message = generateOrderMessage();
                      setOrderMessage(message);
                      setCurrentStep(1);
                      setShowWhatsAppGuide(true);
                    }}
                  >
                    WhatsApp not opening? Send order manually
                  </button>
                </div>

                {!selectedAddressId && (
                  <div className="text-sm text-brand-main-red mb-4 text-center">
                    Please select a delivery address below to proceed with checkout
                  </div>
                )}

                <div className="text-sm text-gray-600 italic mb-4">
                  Payments will be done via WhatsApp and you will be redirected to WhatsApp.
                </div>

                <button
                  onClick={() => navigate('/store')}
                  className="w-full bg-transparent border-2 border-brand-accent-teal text-brand-accent-teal py-3 rounded-full font-semibold hover:bg-brand-accent-teal hover:text-white transition-all duration-300"
                >
                  Continue Shopping
                </button>
              </div>

              {/* Address Selection */}
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6 mt-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Select Delivery Address</h3>
                <AddressList
                  onSelect={true}
                  onAddressSelect={setSelectedAddressId}
                  selectedAddressId={selectedAddressId}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile & Tablet Sticky Bottom Section */}
      {cartItems.length > 0 && (
        <div
          className={`fixed bottom-0 left-0 right-0 z-50 lg:hidden transition-all duration-500 ease-out transform ${
            isStickyBottomVisible && !isOrderSummaryInView
              ? 'opacity-100 translate-y-0 scale-100'
              : 'opacity-0 translate-y-full scale-95 pointer-events-none'
          }`}
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          }}
        >
          {/* Background with glassmorphism */}
          <div className="bg-white/95 backdrop-blur-xl border-t border-gray-200/50 shadow-2xl">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-between">
                {/* Total Amount */}
                <div className="flex-1">
                  <p className="text-sm text-gray-600 mb-1">Total Amount</p>
                  <p className="text-xl font-bold text-brand-main-red">
                    AED {cartSummary.total.toFixed(2)}
                  </p>
                  {cartSummary.totalSavings && cartSummary.totalSavings > 0 && (
                    <p className="text-sm text-brand-accent-teal font-semibold">
                      Save AED {cartSummary.totalSavings.toFixed(2)}
                    </p>
                  )}
                </div>

                {/* Proceed to Checkout Button with Down Arrow */}
                <button
                  onClick={handleScrollToOrderSummary}
                  className="group relative bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white font-bold py-4 px-3 sm:px-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-brand-accent-teal/30 min-h-[44px] min-w-[44px] flex items-center space-x-2 sm:space-x-3"
                  aria-label="Proceed to checkout - scroll to order summary"
                >
                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                  <span className="relative z-10 text-xs sm:text-base font-semibold">Proceed to Checkout</span>

                  {/* Professional Down Arrow */}
                  <div className="relative z-10 bg-white/20 backdrop-blur-sm rounded-full p-2 group-hover:bg-white/30 transition-all duration-300">
                    <ChevronDownIcon
                      size={20}
                      className="transform group-hover:translate-y-1 transition-transform duration-300"
                    />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* WhatsApp Guide Modal */}
      {showWhatsAppGuide && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm">
          <div className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto scrollbar-hide">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-brand-accent-teal to-brand-accent-teal-darker text-white p-6 rounded-t-3xl">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">Manual Order Process</h2>
                  <p className="text-teal-100">
                    {isMobileOrTablet()
                      ? "Send your order via your phone's WhatsApp app"
                      : "Send your order via WhatsApp (phone or web)"
                    }
                  </p>
                </div>
                <button
                  onClick={() => setShowWhatsAppGuide(false)}
                  className="text-white hover:bg-white/20 rounded-full p-2 transition-colors duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Step Progress Indicator */}
            <div className="px-6 py-4 bg-gray-50">
              <div className="flex items-center justify-center space-x-4">
                {Array.from({ length: getTotalSteps() }, (_, i) => i + 1).map((step) => (
                  <div key={step} className="flex items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 ${
                      currentStep >= step
                        ? 'bg-brand-accent-teal text-white shadow-lg'
                        : 'bg-gray-200 text-gray-500'
                    }`}>
                      {step}
                    </div>
                    {step < getTotalSteps() && (
                      <div className={`w-12 h-1 mx-2 rounded-full transition-all duration-300 ${
                        currentStep > step ? 'bg-brand-accent-teal' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Step Content */}
            <div className="p-6">
              {currentStep === 1 && (
                <div className="text-center">
                  {isMobileOrTablet() ? (
                    // Mobile/Tablet Step 1: Copy Order Message
                    <>
                      <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">Step 1: Copy Your Order Message</h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        Since the automatic redirect didn't work, we'll help you send your order manually. First, let's copy your complete order details to your clipboard.
                      </p>
                      <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                        <p className="text-green-800 text-sm">
                          📱 <strong>Perfect for Mobile:</strong> You'll copy the message and send it directly through your phone's WhatsApp app - much easier than using a web browser!
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(orderMessage).then(() => {
                            setCurrentStep(2);
                            alert('✅ Order message copied to clipboard!');
                          }).catch(err => {
                            console.error('Failed to copy message:', err);
                            alert('❌ Failed to copy message. Please try again.');
                          });
                        }}
                        className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                      >
                        Copy Order Message
                      </button>
                    </>
                  ) : (
                    // Desktop Step 1: Choose Method
                    <>
                      <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">Step 1: Choose Your Preferred Method</h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        Since the automatic redirect didn't work, you can send your order manually using one of these convenient options:
                      </p>
                      <div className="space-y-4 mb-6">
                        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                          <div className="flex items-center mb-2">
                            <span className="text-2xl mr-3">📱</span>
                            <h4 className="font-bold text-green-800">Option 1: Use Your Phone (Recommended)</h4>
                          </div>
                          <p className="text-green-700 text-sm">
                            Copy the order message and send it through your phone's WhatsApp app. This is usually the most convenient method.
                          </p>
                        </div>
                        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                          <div className="flex items-center mb-2">
                            <span className="text-2xl mr-3">💻</span>
                            <h4 className="font-bold text-blue-800">Option 2: Use WhatsApp Web</h4>
                          </div>
                          <p className="text-blue-700 text-sm">
                            Open WhatsApp Web in your browser and send the message from your computer.
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-4 justify-center">
                        <button
                          onClick={() => setCurrentStep(2)}
                          className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                        >
                          📱 Use Phone App
                        </button>
                        <button
                          onClick={() => {
                            window.open('https://web.whatsapp.com', '_blank');
                            setCurrentStep(2);
                          }}
                          className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                        >
                          💻 Use WhatsApp Web
                        </button>
                      </div>
                    </>
                  )}
                </div>
              )}

              {currentStep === 2 && (
                <div className="text-center">
                  {isMobileOrTablet() ? (
                    // Mobile/Tablet Step 2: Send via Phone
                    <>
                      <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">Step 2: Send via Your Phone's WhatsApp</h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        Perfect! Your order message is now copied. Now open WhatsApp on your phone and send it to us.
                      </p>
                      <div className="text-left bg-gray-50 rounded-xl p-6 mb-6">
                        <p className="text-gray-700 mb-4 font-semibold">Follow these simple steps on your phone:</p>
                        <ol className="space-y-3 text-gray-600">
                          <li className="flex items-start">
                            <span className="bg-brand-accent-teal text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                            Open WhatsApp on your phone
                          </li>
                          <li className="flex items-start">
                            <span className="bg-brand-accent-teal text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                            Search for <strong>+91 9778248100</strong> or tap "New Chat"
                          </li>
                          <li className="flex items-start">
                            <span className="bg-brand-accent-teal text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                            Long-press in the message box and tap "Paste"
                          </li>
                          <li className="flex items-start">
                            <span className="bg-brand-accent-teal text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                            Tap the Send button
                          </li>
                        </ol>
                      </div>
                      <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                        <p className="text-green-800 text-sm">
                          🎉 <strong>That's it!</strong> We'll receive your order and contact you for payment and delivery details.
                        </p>
                      </div>
                      <button
                        onClick={() => setShowWhatsAppGuide(false)}
                        className="bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                      >
                        Got it! Close Guide
                      </button>
                    </>
                  ) : (
                    // Desktop Step 2: Copy Message
                    <>
                      <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">Step 2: Copy Your Order Message</h3>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        Click the button below to copy your complete order details to your clipboard.
                      </p>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
                        <p className="text-yellow-800 text-sm">
                          📋 <strong>Note:</strong> Your order message will be automatically copied when you click the button.
                        </p>
                      </div>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(orderMessage).then(() => {
                            setCurrentStep(3);
                            alert('✅ Order message copied to clipboard!');
                          }).catch(err => {
                            console.error('Failed to copy message:', err);
                            alert('❌ Failed to copy message. Please try again.');
                          });
                        }}
                        className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                      >
                        Copy Order Message
                      </button>
                    </>
                  )}
                </div>
              )}

              {currentStep === 3 && !isMobileOrTablet() && (
                <div className="text-center">
                  <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Step 3: Send Your Order</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Great! Your order message is copied. Now choose how you'd like to send it:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {/* Phone Option */}
                    <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                      <div className="flex items-center justify-center mb-4">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-2xl">📱</span>
                        </div>
                      </div>
                      <h4 className="font-bold text-green-800 mb-3">Use Your Phone (Recommended)</h4>
                      <div className="text-left text-sm text-green-700 mb-4 space-y-2">
                        <p>• Open WhatsApp on your phone</p>
                        <p>• Search for <strong>+91 9778248100</strong></p>
                        <p>• Paste and send the message</p>
                      </div>
                      <div className="bg-green-100 rounded-lg p-3 mb-4">
                        <p className="text-green-800 text-xs">
                          ✨ <strong>Why this is better:</strong> Faster, more reliable, and you're probably already logged into WhatsApp on your phone!
                        </p>
                      </div>
                    </div>

                    {/* Web Option */}
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                      <div className="flex items-center justify-center mb-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-2xl">💻</span>
                        </div>
                      </div>
                      <h4 className="font-bold text-blue-800 mb-3">Use WhatsApp Web</h4>
                      <div className="text-left text-sm text-blue-700 mb-4 space-y-2">
                        <p>• Go to web.whatsapp.com</p>
                        <p>• Scan QR code with your phone</p>
                        <p>• Search for <strong>+91 9778248100</strong></p>
                        <p>• Paste and send (Ctrl+V)</p>
                      </div>
                      <button
                        onClick={() => window.open('https://web.whatsapp.com', '_blank')}
                        className="w-full bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-600 transition-colors duration-200"
                      >
                        Open WhatsApp Web
                      </button>
                    </div>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-xl p-4 mb-6">
                    <p className="text-green-800 text-sm">
                      🎉 <strong>That's it!</strong> We'll receive your order and contact you for payment and delivery details.
                    </p>
                  </div>

                  <div className="flex space-x-4 justify-center">
                    <button
                      onClick={() => window.open('https://wa.me/************', '_blank')}
                      className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300"
                    >
                      📱 Open Chat with Le Prestine
                    </button>
                    <button
                      onClick={() => setShowWhatsAppGuide(false)}
                      className="bg-gray-500 text-white px-6 py-3 rounded-full font-semibold hover:bg-gray-600 transition-all duration-300"
                    >
                      Close Guide
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Navigation Buttons */}
            {currentStep < getTotalSteps() && (
              <div className="px-6 pb-6 flex justify-between">
                <button
                  onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                  disabled={currentStep === 1}
                  className={`px-6 py-2 rounded-full font-semibold transition-all duration-300 ${
                    currentStep === 1
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-gray-500 text-white hover:bg-gray-600'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentStep(Math.min(getTotalSteps(), currentStep + 1))}
                  className="bg-brand-accent-teal text-white px-6 py-2 rounded-full font-semibold hover:bg-brand-accent-teal-darker transition-all duration-300"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
