import React, { useEffect, useRef, useState } from 'react';

interface CaptchaProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  className?: string;
  theme?: 'light' | 'dark';
  size?: 'compact' | 'normal';
}

declare global {
  interface Window {
    hcaptcha: any;
    onHcaptchaLoad: () => void;
  }
}

const Captcha: React.FC<CaptchaProps> = ({
  onVerify,
  onError,
  onExpire,
  className = '',
  theme = 'light',
  size = 'normal'
}) => {
  const captchaRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [widgetId, setWidgetId] = useState<string | null>(null);
  const [isRendering, setIsRendering] = useState(false);
  const containerId = useRef(`hcaptcha-${Math.random().toString(36).substring(2, 11)}`);

  useEffect(() => {
    // Load hCaptcha script if not already loaded
    if (!window.hcaptcha) {
      const script = document.createElement('script');
      script.src = 'https://js.hcaptcha.com/1/api.js?onload=onHcaptchaLoad&render=explicit';
      script.async = true;
      script.defer = true;
      
      window.onHcaptchaLoad = () => {
        setIsLoaded(true);
      };
      
      document.head.appendChild(script);
    } else {
      setIsLoaded(true);
    }

    return () => {
      // Cleanup
      if (widgetId && window.hcaptcha) {
        try {
          console.log('Cleaning up hCaptcha widget:', widgetId);
          window.hcaptcha.remove(widgetId);
          setWidgetId(null);
          setIsRendering(false);
        } catch (error) {
          console.warn('Error removing hCaptcha widget:', error);
        }
      }
    };
  }, []);

  useEffect(() => {
    if (isLoaded && captchaRef.current && window.hcaptcha && !widgetId && !isRendering) {
      setIsRendering(true);

      // Small delay to ensure DOM is ready
      setTimeout(() => {
        if (captchaRef.current) {
          try {
            console.log('Rendering hCaptcha with container:', captchaRef.current);
            const id = window.hcaptcha.render(captchaRef.current, {
              sitekey: (import.meta as any).env.VITE_HCAPTCHA_SITE_KEY || '10000000-ffff-ffff-ffff-000000000001', // Default test key
              theme: theme,
              size: size,
              callback: (token: string) => {
                console.log('Captcha verified:', token);
                onVerify(token);
              },
              'error-callback': (error: string) => {
                console.error('Captcha error:', error);
                onError?.(error);
                setIsRendering(false);
              },
              'expired-callback': () => {
                console.log('Captcha expired');
                onExpire?.();
              }
            });
            console.log('hCaptcha rendered with ID:', id);
            setWidgetId(id);
            setIsRendering(false);
          } catch (error) {
            console.error('Error rendering captcha:', error);
            onError?.('Failed to load captcha');
            setIsRendering(false);
          }
        } else {
          setIsRendering(false);
        }
      }, 100);
    }
  }, [isLoaded, theme, size, onVerify, onError, onExpire, widgetId, isRendering]);

  const reset = () => {
    if (widgetId && window.hcaptcha) {
      try {
        window.hcaptcha.reset(widgetId);
      } catch (error) {
        console.warn('Error resetting captcha:', error);
      }
    }
  };

  // Create a separate ref for imperative handle
  const resetRef = useRef<{ reset: () => void }>(null);

  // Expose reset method to parent components
  React.useImperativeHandle(resetRef, () => ({
    reset
  }));

  return (
    <div className={`captcha-container ${className}`}>
      <div
        ref={captchaRef}
        id={containerId.current}
        className="flex justify-center items-center min-h-[78px] bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-4"
      >
        {!isLoaded && (
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-brand-accent-teal"></div>
            <span className="text-sm">Loading security check...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Captcha;
