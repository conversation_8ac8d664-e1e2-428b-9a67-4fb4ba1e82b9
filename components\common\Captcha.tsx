import React, { useEffect, useRef, useState } from 'react';

interface CaptchaProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  className?: string;
  theme?: 'light' | 'dark';
  size?: 'compact' | 'normal';
}

declare global {
  interface Window {
    hcaptcha: any;
    onHcaptchaLoad: () => void;
  }
}

const Captcha: React.FC<CaptchaProps> = ({
  onVerify,
  onError,
  onExpire,
  className = '',
  theme = 'light',
  size = 'normal'
}) => {
  const captchaRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [widgetId, setWidgetId] = useState<string | null>(null);

  useEffect(() => {
    // Load hCaptcha script if not already loaded
    if (!window.hcaptcha) {
      const script = document.createElement('script');
      script.src = 'https://js.hcaptcha.com/1/api.js?onload=onHcaptchaLoad&render=explicit';
      script.async = true;
      script.defer = true;
      
      window.onHcaptchaLoad = () => {
        setIsLoaded(true);
      };
      
      document.head.appendChild(script);
    } else {
      setIsLoaded(true);
    }

    return () => {
      // Cleanup
      if (widgetId && window.hcaptcha) {
        try {
          window.hcaptcha.remove(widgetId);
        } catch (error) {
          console.warn('Error removing hCaptcha widget:', error);
        }
      }
    };
  }, []);

  useEffect(() => {
    if (isLoaded && captchaRef.current && window.hcaptcha && !widgetId) {
      try {
        const id = window.hcaptcha.render(captchaRef.current, {
          sitekey: import.meta.env.VITE_HCAPTCHA_SITE_KEY || '10000000-ffff-ffff-ffff-000000000001', // Default test key
          theme: theme,
          size: size,
          callback: (token: string) => {
            console.log('Captcha verified:', token);
            onVerify(token);
          },
          'error-callback': (error: string) => {
            console.error('Captcha error:', error);
            onError?.(error);
          },
          'expired-callback': () => {
            console.log('Captcha expired');
            onExpire?.();
          }
        });
        setWidgetId(id);
      } catch (error) {
        console.error('Error rendering captcha:', error);
        onError?.('Failed to load captcha');
      }
    }
  }, [isLoaded, theme, size, onVerify, onError, onExpire, widgetId]);

  const reset = () => {
    if (widgetId && window.hcaptcha) {
      try {
        window.hcaptcha.reset(widgetId);
      } catch (error) {
        console.warn('Error resetting captcha:', error);
      }
    }
  };

  // Expose reset method to parent components
  React.useImperativeHandle(captchaRef, () => ({
    reset
  }));

  return (
    <div className={`captcha-container ${className}`}>
      <div 
        ref={captchaRef}
        className="flex justify-center items-center min-h-[78px] bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-4"
      >
        {!isLoaded && (
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-brand-accent-teal"></div>
            <span className="text-sm">Loading security check...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Captcha;
